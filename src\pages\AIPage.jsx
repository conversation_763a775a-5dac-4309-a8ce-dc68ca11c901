import React, { useEffect, useState, useRef } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Card } from 'react-bootstrap';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const aiPageData = {
  hero: {
    title: "Intelligent Solutions for Tomorrow",
    subtitle: "Harness the power of Artificial Intelligence to transform your business operations, automate complex processes, and unlock unprecedented insights from your data. Our AI solutions drive innovation and competitive advantage.",
    backgroundImage: "https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
  },
  intro: {
    title: "Leading AI Innovation & Implementation",
    description: "At Makonis, we specialize in developing cutting-edge AI solutions that revolutionize how businesses operate. From machine learning algorithms to neural networks, we deliver intelligent systems that learn, adapt, and optimize your operations for maximum efficiency and growth.",
    image: "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
  },
  services: [
    {
      id: "machine-learning",
      title: "Machine Learning Solutions",
      icon: "fa-brain",
      description: "Build intelligent systems that learn from data patterns and make accurate predictions. Our ML solutions automate decision-making and provide actionable insights for your business.",
      features: [
        "Predictive Analytics & Forecasting",
        "Classification & Regression Models",
        "Recommendation Systems",
        "Anomaly Detection",
        "Custom Algorithm Development"
      ],
      image: "https://images.unsplash.com/photo-1555255707-c07966088b7b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
    },
    {
      id: "nlp",
      title: "Natural Language Processing",
      icon: "fa-comments",
      description: "Transform unstructured text data into valuable insights. Our NLP solutions enable machines to understand, interpret, and generate human language with remarkable accuracy.",
      features: [
        "Sentiment Analysis & Opinion Mining",
        "Chatbots & Virtual Assistants",
        "Document Processing & Extraction",
        "Language Translation Services",
        "Text Summarization & Generation"
      ],
      image: "https://images.unsplash.com/photo-1531746790731-6c087fecd65a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
    },
    {
      id: "computer-vision",
      title: "Computer Vision",
      icon: "fa-eye",
      description: "Enable machines to see and interpret visual information like humans. Our computer vision solutions automate visual inspection, recognition, and analysis tasks.",
      features: [
        "Image & Video Recognition",
        "Object Detection & Tracking",
        "Facial Recognition Systems",
        "Quality Control Automation",
        "Medical Image Analysis"
      ],
      image: "https://images.unsplash.com/photo-1507146426996-ef05306b995a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
    },
    {
      id: "deep-learning",
      title: "Deep Learning & Neural Networks",
      icon: "fa-project-diagram",
      description: "Leverage advanced neural networks to solve complex problems. Our deep learning solutions handle sophisticated pattern recognition and decision-making tasks.",
      features: [
        "Convolutional Neural Networks (CNNs)",
        "Recurrent Neural Networks (RNNs)",
        "Generative Adversarial Networks (GANs)",
        "Transfer Learning Applications",
        "Custom Architecture Design"
      ],
      image: "https://images.unsplash.com/photo-**********-ef010cbdcc31?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
    }
  ],
  process: {
    title: "Our AI Development Methodology",
    description: "We follow a systematic approach to AI implementation, ensuring successful deployment and measurable business impact through every phase of development.",
    steps: [
      {
        title: "Discovery & Assessment",
        description: "We analyze your business challenges and identify AI opportunities that deliver maximum value and ROI.",
        icon: "fa-search"
      },
      {
        title: "Data Strategy & Preparation",
        description: "We design comprehensive data collection and preprocessing strategies to ensure high-quality training datasets.",
        icon: "fa-database"
      },
      {
        title: "Model Development & Training",
        description: "Our experts develop and train custom AI models tailored to your specific business requirements and objectives.",
        icon: "fa-cogs"
      },
      {
        title: "Testing & Validation",
        description: "We rigorously test AI models for accuracy, performance, and reliability before deployment to production environments.",
        icon: "fa-check-circle"
      },
      {
        title: "Deployment & Integration",
        description: "We seamlessly integrate AI solutions into your existing systems with minimal disruption to operations.",
        icon: "fa-rocket"
      },
      {
        title: "Monitoring & Optimization",
        description: "We provide ongoing monitoring and continuous improvement to ensure optimal AI performance and adaptation.",
        icon: "fa-chart-line"
      }
    ]
  },
  stats: [
    { value: "95%", label: "Model Accuracy Rate", icon: "fa-bullseye" },
    { value: "60%", label: "Process Automation Increase", icon: "fa-robot" },
    { value: "40%", label: "Cost Reduction Average", icon: "fa-dollar-sign" },
    { value: "24/7", label: "AI System Monitoring", icon: "fa-shield-alt" }
  ],
  cta: {
    title: "Ready to Transform Your Business with AI?",
    text: "Partner with us to unlock the full potential of artificial intelligence. Let's discuss how our AI expertise can drive innovation and growth for your organization.",
    buttonText: "Schedule AI Consultation",
    buttonLink: "/contact"
  }
};

const AIPage = () => {
  const [activeService, setActiveService] = useState("machine-learning");
  const heroRef = useRef(null);
  const dashboardRef = useRef(null);
  const sectionsRef = useRef([]);
  const processTimelineRef = useRef(null);

  const secondaryColor = "#00a0e9";

  const gradientTextStyle = {
    fontSize: "clamp(2.5rem, 6vw, 3.5rem)",
    lineHeight: "1.2",
    fontWeight: "bold",
    background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent",
    backgroundClip: "text",
    textShadow: "0 0 40px rgba(0, 160, 233, 0.4)"
  };

  const glassCardStyle = {
    background: "rgba(255, 255, 255, 0.05)",
    backdropFilter: "blur(15px)",
    border: "1px solid rgba(255, 255, 255, 0.1)",
    borderRadius: "1rem",
    transition: "all 0.4s ease"
  };

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  useEffect(() => {
    window.scrollTo(0, 0);

    const ctx = gsap.context(() => {
      // Hero section animations
      if (heroRef.current) {
        gsap.from(heroRef.current.querySelector("h1"), { 
          y: 100, 
          opacity: 0, 
          duration: 1.2, 
          ease: "power3.out", 
          delay: 0.2 
        });
        gsap.from(heroRef.current.querySelector("p"), { 
          y: 50, 
          opacity: 0, 
          duration: 1, 
          ease: "power2.out", 
          delay: 0.5 
        });
        if (dashboardRef.current) {
          gsap.from(dashboardRef.current, { 
            y: 50, 
            opacity: 0, 
            duration: 1.2, 
            ease: "power3.out", 
            delay: 0.8 
          });
        }
      }

      // Section animations
      sectionsRef.current.forEach((section) => {
        const targets = section.querySelectorAll(".animate-in");
        if (targets.length > 0) {
          gsap.from(targets, {
            y: 60,
            opacity: 0,
            duration: 1,
            ease: "power3.out",
            stagger: 0.2,
            scrollTrigger: {
              trigger: section,
              start: "top 85%",
              toggleActions: "play none none reverse"
            }
          });
        }
      });
    });

    return () => {
      ScrollTrigger.getAll().forEach(t => t.kill());
      ctx.revert();
    };
  }, []);

  const addToRefs = (el) => {
    if (el && !sectionsRef.current.includes(el)) {
      sectionsRef.current.push(el);
    }
  };

  return (
    <div style={{ overflowX: 'hidden' }}>
      {/* CSS Animations */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          33% { transform: translateY(-10px) rotate(1deg); }
          66% { transform: translateY(5px) rotate(-1deg); }
        }

        @keyframes fadeInLeft {
          from {
            opacity: 0;
            transform: translateX(-30px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }

        @keyframes pulse {
          0%, 100% {
            opacity: 0.3;
            transform: scale(1);
          }
          50% {
            opacity: 0.8;
            transform: scale(1.1);
          }
        }

        .stat-item {
          opacity: 0;
        }

        .service-card {
          transition: all 0.3s ease;
        }

        .service-card:hover {
          transform: translateY(-5px) !important;
        }

        .process-item-enhanced {
          opacity: 0;
          transform: translateY(30px);
        }

        .animate-in {
          opacity: 0;
          transform: translateY(30px);
        }
      `}</style>

      {/* Hero Section - Analytics page style */}
      <div
        ref={heroRef}
        style={{
          position: 'relative',
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          background: 'linear-gradient(135deg, #002956 0%, #001a3a 100%)',
          color: 'white',
          overflow: 'hidden'
        }}
      >
        {/* Animated background */}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `url("${aiPageData.hero.backgroundImage}")`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            opacity: 0.15,
            zIndex: 0
          }}
        ></div>

        {/* Animated overlay */}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at center, rgba(0,160,233,0.1) 0%, rgba(26,26,46,0.1) 70%)',
            zIndex: 1
          }}
        ></div>

        {/* Floating particles */}
        {[...Array(30)].map((_, i) => (
          <div
            key={i}
            style={{
              position: 'absolute',
              width: `${Math.random() * 10 + 3}px`,
              height: `${Math.random() * 10 + 3}px`,
              backgroundColor: 'rgba(0, 160, 233, 0.3)',
              borderRadius: '50%',
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animation: `float ${Math.random() * 10 + 10}s linear infinite`,
              animationDelay: `${Math.random() * 5}s`,
              zIndex: 1
            }}
          ></div>
        ))}

        <Container>
          <Row className="align-items-center">
            <Col lg={6} className="position-relative" style={{ zIndex: 2, animation: 'fadeInLeft 1s ease-out' }}>
              <h1
                style={{
                  fontSize: "3.6rem",
                  fontWeight: "800",
                  letterSpacing: "2.6px",
                  marginBottom: "1rem",
                  background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
                }}
              >
                {aiPageData.hero.title}
              </h1>

              <p
                style={{
                  fontSize: '1.3rem',
                  lineHeight: 1.6,
                  marginBottom: '2rem',
                  color: '#e1e1e1'
                }}
              >
                {aiPageData.hero.subtitle}
              </p>

              <div
                style={{
                  marginTop: '2.5rem',
                  display: 'flex',
                  gap: '20px',
                  alignItems: 'center'
                }}
              >
                <Button
                  onClick={() => scrollToSection('machine-learning')}
                  style={{
                    background: 'linear-gradient(90deg, #0056b3, #00a0e9)',
                    border: 'none',
                    borderRadius: '50px',
                    padding: '15px 30px',
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    boxShadow: '0 10px 20px rgba(0, 86, 179, 0.3)',
                    transition: 'all 0.3s ease',
                    display: 'inline-flex',
                    alignItems: 'center'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.boxShadow = '0 15px 30px rgba(0, 86, 179, 0.4)';
                    e.currentTarget.style.transform = 'translateY(-3px)';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.boxShadow = '0 10px 20px rgba(0, 86, 179, 0.3)';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                >
                  Explore AI Solutions <i className="fas fa-arrow-right ms-2"></i>
                </Button>

                <Link
                  to="/contact"
                  style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    color: 'white',
                    textDecoration: 'none',
                    fontSize: '1.1rem',
                    fontWeight: 500,
                    transition: 'all 0.3s ease'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.color = '#00a0e9';
                    e.currentTarget.style.transform = 'translateX(5px)';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.color = 'white';
                    e.currentTarget.style.transform = 'translateX(0)';
                  }}
                >
                  Contact Us <i className="fas fa-long-arrow-alt-right ms-2"></i>
                </Link>
              </div>

              {/* Statistics */}
              <div
                style={{
                  marginTop: '3rem',
                  display: 'flex',
                  gap: '30px'
                }}
              >
                {['95%', '60%', '40%'].map((stat, index) => (
                  <div
                    key={index}
                    className="stat-item"
                    style={{
                      textAlign: 'center',
                      animation: 'fadeInLeft 1s ease-out forwards',
                      animationDelay: `${0.3 * (index + 1)}s`,
                      opacity: 0
                    }}
                  >
                    <div
                      style={{
                        fontSize: '2rem',
                        fontWeight: 700,
                        color: '#00a0e9',
                        marginBottom: '5px'
                      }}
                    >
                      {stat}
                    </div>
                    <div
                      style={{
                        fontSize: '0.9rem',
                        color: '#e1e1e1',
                        opacity: 0.8
                      }}
                    >
                      {index === 0 ? 'Model Accuracy' : index === 1 ? 'Automation Increase' : 'Cost Reduction'}
                    </div>
                  </div>
                ))}
              </div>
            </Col>
            <Col lg={6} className="position-relative d-none d-lg-block" style={{ height: '100vh' }}>
              {/* AI Visualization */}
              <div
                ref={dashboardRef}
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%) perspective(1000px) rotateY(-15deg) rotateX(10deg)',
                  width: '90%',
                  height: '400px',
                  background: 'rgba(255, 255, 255, 0.05)',
                  borderRadius: '20px',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)',
                  overflow: 'hidden',
                  zIndex: 5,
                  animation: 'float 6s ease-in-out infinite'
                }}
              >
                <div
                  style={{
                    position: 'absolute',
                    top: '20px',
                    left: '20px',
                    right: '20px',
                    height: '60px',
                    background: 'rgba(0, 160, 233, 0.1)',
                    borderRadius: '10px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '1.2rem',
                    fontWeight: 600,
                    color: 'white'
                  }}
                >
                  <i className="fas fa-brain me-2"></i>
                  AI Neural Network
                </div>

                {/* Neural network visualization */}
                <div
                  style={{
                    position: 'absolute',
                    top: '100px',
                    left: '20px',
                    right: '20px',
                    bottom: '20px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-around'
                  }}
                >
                  {[...Array(3)].map((_, layerIndex) => (
                    <div key={layerIndex} style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                      {[...Array(4)].map((_, nodeIndex) => (
                        <div
                          key={nodeIndex}
                          style={{
                            width: '20px',
                            height: '20px',
                            borderRadius: '50%',
                            background: `rgba(0, 160, 233, ${0.3 + Math.random() * 0.4})`,
                            animation: `pulse ${2 + Math.random() * 2}s infinite ease-in-out`,
                            animationDelay: `${Math.random() * 2}s`
                          }}
                        ></div>
                      ))}
                    </div>
                  ))}
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      {/* Introduction Section - Testing page style */}
      <section
        ref={addToRefs}
        className="py-5 my-5"
        style={{
          background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
          backdropFilter: "blur(10px)",
          color: "rgba(255, 255, 255, 0.9)"
        }}
      >
        <Container>
          <Row className="align-items-center g-5">
            <Col lg={7} className="animate-in">
              <h2 style={gradientTextStyle}>{aiPageData.intro.title}</h2>
              <p className="lead mt-4 mb-4" style={{ color: "rgba(255,255,255,0.8)" }}>
                {aiPageData.intro.description}
              </p>
            </Col>
            <Col lg={5} className="animate-in">
              <div className="p-3" style={glassCardStyle}>
                <img
                  src={aiPageData.intro.image}
                  alt="AI Innovation"
                  className="img-fluid rounded-3"
                  style={{ minHeight: "350px", objectFit: "cover" }}
                />
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Statistics Section */}
      <section
        ref={addToRefs}
        className="py-5"
        style={{
          background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
          backdropFilter: "blur(10px)",
          color: "rgba(255, 255, 255, 0.9)"
        }}
      >
        <Container>
          <Row className="g-4">
            {aiPageData.stats.map((stat, index) => (
              <Col md={6} lg={3} key={index} className="animate-in">
                <div
                  className="text-center p-4 h-100 stat-card"
                  style={glassCardStyle}
                >
                  <i
                    className={`fas ${stat.icon} fa-3x mb-3`}
                    style={{ color: secondaryColor }}
                  ></i>
                  <h3 className="display-5 fw-bold text-white">{stat.value}</h3>
                  <p className="mb-0 text-white-50">{stat.label}</p>
                </div>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* AI Services Section */}
      <section
        ref={addToRefs}
        className="py-5"
        style={{
          background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
          backdropFilter: "blur(10px)",
          color: "rgba(255, 255, 255, 0.9)"
        }}
      >
        <Container>
          <div className="text-center mb-5 animate-in">
            <h2 style={gradientTextStyle}>Our AI Service Portfolio</h2>
            <p className="lead mt-4" style={{ color: "rgba(255,255,255,0.8)" }}>
              Comprehensive artificial intelligence solutions tailored to your business needs
            </p>
          </div>

          <Row className="g-4">
            {aiPageData.services.map((service, index) => (
              <Col lg={6} key={service.id} className="animate-in">
                <Card
                  className="h-100 service-card"
                  style={{
                    ...glassCardStyle,
                    cursor: 'pointer',
                    transform: activeService === service.id ? 'scale(1.02)' : 'scale(1)',
                    border: activeService === service.id ? '2px solid #00a0e9' : '1px solid rgba(255, 255, 255, 0.1)'
                  }}
                  onClick={() => setActiveService(service.id)}
                  onMouseEnter={(e) => {
                    if (activeService !== service.id) {
                      e.currentTarget.style.transform = 'scale(1.01)';
                      e.currentTarget.style.border = '1px solid rgba(0, 160, 233, 0.5)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (activeService !== service.id) {
                      e.currentTarget.style.transform = 'scale(1)';
                      e.currentTarget.style.border = '1px solid rgba(255, 255, 255, 0.1)';
                    }
                  }}
                >
                  <Card.Body className="p-4">
                    <div className="d-flex align-items-center mb-3">
                      <i
                        className={`fas ${service.icon} fa-2x me-3`}
                        style={{ color: secondaryColor }}
                      ></i>
                      <h4 className="mb-0 text-white">{service.title}</h4>
                    </div>

                    <p className="text-white-50 mb-4">{service.description}</p>

                    <div className="mb-4">
                      <img
                        src={service.image}
                        alt={service.title}
                        className="img-fluid rounded-3"
                        style={{ height: "200px", width: "100%", objectFit: "cover" }}
                      />
                    </div>

                    <ul className="list-unstyled">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="mb-2 text-white-50">
                          <i className="fas fa-check-circle me-2" style={{ color: secondaryColor }}></i>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* Process Methodology Section */}
      <section
        ref={addToRefs}
        className="py-5"
        style={{
          background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
          backdropFilter: "blur(10px)",
          color: "rgba(255, 255, 255, 0.9)"
        }}
      >
        <Container>
          <div className="text-center mb-5 animate-in">
            <h2 style={gradientTextStyle}>{aiPageData.process.title}</h2>
            <p className="lead mt-4" style={{ color: "rgba(255,255,255,0.8)" }}>
              {aiPageData.process.description}
            </p>
          </div>

          <div ref={processTimelineRef} className="position-relative">
            {/* Timeline line */}
            <div
              className="process-track-line position-absolute"
              style={{
                left: '50%',
                top: '0',
                bottom: '0',
                width: '2px',
                background: 'linear-gradient(to bottom, #00a0e9, rgba(0, 160, 233, 0.3))',
                transform: 'translateX(-50%)',
                zIndex: 1
              }}
            ></div>

            {aiPageData.process.steps.map((step, index) => (
              <div
                key={index}
                className="process-item-enhanced mb-5 animate-in"
                style={{ position: 'relative' }}
              >
                <Row className="align-items-center">
                  <Col md={5} className={index % 2 === 0 ? 'text-end' : 'order-md-2'}>
                    <div
                      className="process-card-enhanced p-4"
                      style={{
                        ...glassCardStyle,
                        marginBottom: index % 2 === 0 ? '0' : '0',
                        marginLeft: index % 2 === 0 ? 'auto' : '0',
                        marginRight: index % 2 === 0 ? '0' : 'auto',
                        maxWidth: '400px'
                      }}
                    >
                      <div className="d-flex align-items-center mb-3">
                        <i
                          className={`fas ${step.icon} fa-2x me-3`}
                          style={{ color: secondaryColor }}
                        ></i>
                        <h5 className="mb-0 text-white">{step.title}</h5>
                      </div>
                      <p className="mb-0 text-white-50">{step.description}</p>
                    </div>
                  </Col>

                  <Col md={2} className="text-center">
                    <div
                      className="process-dot-enhanced"
                      style={{
                        width: '20px',
                        height: '20px',
                        borderRadius: '50%',
                        background: secondaryColor,
                        margin: '0 auto',
                        position: 'relative',
                        zIndex: 2,
                        boxShadow: `0 0 20px ${secondaryColor}`
                      }}
                    ></div>
                  </Col>

                  <Col md={5} className={index % 2 === 1 ? 'text-start' : 'order-md-1'}>
                    {/* Empty column for spacing */}
                  </Col>
                </Row>
              </div>
            ))}
          </div>
        </Container>
      </section>

      {/* Call to Action Section */}
      <section
        ref={addToRefs}
        className="py-5"
        style={{
          background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
          backdropFilter: "blur(10px)",
          color: "rgba(255, 255, 255, 0.9)"
        }}
      >
        <Container>
          <Row className="justify-content-center text-center">
            <Col lg={8} className="animate-in">
              <div className="p-5" style={glassCardStyle}>
                <h2 style={gradientTextStyle}>{aiPageData.cta.title}</h2>
                <p className="lead mt-4 mb-4" style={{ color: "rgba(255,255,255,0.8)" }}>
                  {aiPageData.cta.text}
                </p>
                <Link
                  to={aiPageData.cta.buttonLink}
                  className="btn btn-lg"
                  style={{
                    background: 'linear-gradient(90deg, #0056b3, #00a0e9)',
                    border: 'none',
                    borderRadius: '50px',
                    padding: '15px 40px',
                    fontSize: '1.2rem',
                    fontWeight: 600,
                    color: 'white',
                    textDecoration: 'none',
                    boxShadow: '0 10px 20px rgba(0, 86, 179, 0.3)',
                    transition: 'all 0.3s ease',
                    display: 'inline-flex',
                    alignItems: 'center'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.boxShadow = '0 15px 30px rgba(0, 86, 179, 0.4)';
                    e.currentTarget.style.transform = 'translateY(-3px)';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.boxShadow = '0 10px 20px rgba(0, 86, 179, 0.3)';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                >
                  {aiPageData.cta.buttonText} <i className="fas fa-arrow-right ms-2"></i>
                </Link>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
    </div>
  );
};

export default AIPage;
