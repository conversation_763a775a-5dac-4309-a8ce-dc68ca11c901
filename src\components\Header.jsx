import React, { useState, useRef, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import MakonisLogo from "../Asserts/Makonis-Logo.png";
import SearchOverlay from "./SearchOverlay";
import { IoIosArrowDown } from "react-icons/io";

const Header = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [openDropdown, setOpenDropdown] = useState("");
  const [isDesktopSearchOpen, setIsDesktopSearchOpen] = useState(false);
  const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);
  const [openMobileSubMenu, setOpenMobileSubMenu] = useState("");
  const [openMobileSubDropdown, setOpenMobileSubDropdown] = useState("");
  const [activeSubMenu, setActiveSubMenu] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);

  const location = useLocation();
  const navigate = useNavigate();
  const headerRef = useRef(null);
  const desktopSearchRef = useRef(null);
  const menuTimeoutRef = useRef(null);

  const transitionAllDuration500EaseOut =
    "transition-all duration-500 ease-out";

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const toggleMobileSubMenu = (menuName) => {
    setOpenMobileSubMenu(openMobileSubMenu === menuName ? "" : menuName);
    // Close any open sub-dropdowns when main menu changes
    if (openMobileSubMenu !== menuName) {
      setOpenMobileSubDropdown("");
    }
  };

  const toggleMobileSubDropdown = (dropdownName) => {
    setOpenMobileSubDropdown(
      openMobileSubDropdown === dropdownName ? "" : dropdownName
    );
  };

  const handleMenuClose = () => {
    menuTimeoutRef.current = setTimeout(() => {
      setOpenDropdown("");
    }, 200);
  };

  const handleMenuEnter = () => {
    clearTimeout(menuTimeoutRef.current);
  };

  const handleDesktopSearch = async (query) => {
    setSearchQuery(query);

    if (!query || query.trim().length < 2) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    try {
      // Import search utilities dynamically
      const { getSearchSuggestions } = await import("../contexts/searchUtils");
      const results = getSearchSuggestions(query.trim(), 8);
      setSearchResults(results);
    } catch (error) {
      console.error("Search error:", error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSearchResultClick = (result) => {
    navigate(result.path);
    setIsDesktopSearchOpen(false);
    setSearchQuery("");
    setSearchResults([]);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      setIsDesktopSearchOpen(false);
    }
  };

  useEffect(() => {
    if (headerRef.current) {
      headerRef.current.style.opacity = "0";
      headerRef.current.style.transform = "translateY(-20px)";
      setTimeout(() => {
        if (headerRef.current) {
          headerRef.current.style.transition = "all 0.8s ease";
          headerRef.current.style.opacity = "1";
          headerRef.current.style.transform = "translateY(0)";
        }
      }, 100);
    }
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      // Close any open dropdown if clicking outside
      if (!event.target.closest(".dropdown")) {
        setOpenDropdown("");
      }
      if (
        desktopSearchRef.current &&
        !desktopSearchRef.current.contains(event.target)
      ) {
        setIsDesktopSearchOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    document.body.style.overflow = mobileMenuOpen ? "hidden" : "unset";
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [mobileMenuOpen]);

  const navItems = [
    { name: "Home", path: "/" },
    {
      name: "Who we are",
      path: "/about-us",
      isMegaMenu: true,
      megaMenuItems: [
        { name: "About Us", path: "/about-us" },
        { name: "Leadership", path: "/leadership" },
        { name: "Our Business Model", path: "/business-model" },
      ],
    },
    {
      name: "Business",
      path: "/business",
      isMegaMenu: true,
      megaMenuItems: [
        {
          name: "IT Services",
          path: "/services",
          subMenuItems: [
            {
              name: "Staff Augmentation",
              path: "/services/staff-augmentation-detail",
            },
            { name: "Enterprise RPO", path: "/services/enterprise-rpo" },
            { name: "Contract Staffing", path: "/services/contract-staffing" },
            { name: "Hire Train Deploy", path: "/services/hire-train-deploy" },
            { name: "Integration", path: "/integration" },
            { name: "Testing", path: "/services/testing" },
          ],
        },
        {
          name: "Products",
          path: "/products",
          subMenuItems: [
            { name: "Mako-Plus", path: "/mako-plus" },
            { name: "ATS Demo", path: "/ats-demo" },
            { name: "Trading Intelligence", path: "/trading-intelligence" },
          ],
        },
        {
          name: "Semiconductors",
          path: "/semiconductors",
          subMenuItems: [
            {
              name: "Physical Design",
              path: "/semiconductors/physical-design",
            },
            {
              name: "Physical Verification",
              path: "/semiconductors/physical-verification",
            },
          ],
        },
      ],
    },

 
    { name: "Testimonials", path: "/testimonials" },
    { name: "Careers", path: "/careers" },
    { name: "Contact Us", path: "/contact" },
  ];

  // Mobile navigation items (Business contains sub-dropdowns)
  const mobileNavItems = [
    { name: "Home", path: "/" },
    {
      name: "Who we are",
      path: "/about-us",
      isDropdown: true,
      dropdownItems: [
        { name: "About Us", path: "/about-us" },
        { name: "Leadership", path: "/leadership" },
        { name: "Our Business Model", path: "/business-model" },
      ],
    },
    {
      name: "Business",
      path: "/business",
      isDropdown: true,
      dropdownItems: [
        {
          name: "IT Services",
          path: "/services",
          isSubDropdown: true,
          subDropdownItems: [
            {
              name: "Staff Augmentation",
              path: "/services/staff-augmentation",
            },
            { name: "Enterprise RPO", path: "/services/enterprise-rpo" },
            { name: "Contract Staffing", path: "/services/contract-staffing" },
            { name: "Hire Train Deploy", path: "/services/hire-train-deploy" },
            { name: "Integration", path: "/integration" },
            { name: "Testing", path: "/services/testing" },
          ],
        },
        {
          name: "Products",
          path: "/products",
          isSubDropdown: true,
          subDropdownItems: [
            { name: "Mako-Plus", path: "/products/ai" },
            { name: "ATS Demo", path: "/ats-demo" },
            { name: "Trading Intelligence", path: "/trading-intelligence" },
          ],
        },
        {
          name: "Semiconductors",
          path: "/semiconductors",
          isSubDropdown: true,
          subDropdownItems: [
            {
              name: "Physical Design",
              path: "/semiconductors/physical-design",
            },
            {
              name: "Physical Verification",
              path: "/semiconductors/physical-verification",
            },
          ],
        },
      ],
    },

    { name: "Testimonials", path: "/testimonials" },
    { name: "Careers", path: "/careers" },
    { name: "Contact Us", path: "/contact" },
  ];

  // Get submenu data for mega menu
  const businessMenuItem = navItems.find((item) => item.name === "Business");
  const subMenuData = businessMenuItem?.megaMenuItems?.find(
    (sub) => sub.name === activeSubMenu
  );

  const headerStyle = {
    background:
      "linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%)",
    backdropFilter: "blur(20px)",
    boxShadow: "0 4px 24px rgba(0, 41, 86, 0.08)",
    padding: "8px 0",
    transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
    borderBottom: "1px solid rgba(0, 41, 86, 0.08)",
    zIndex: 1050,
    position: "sticky",
    top: 0,
    width: "100%",
  };

  return (
    <>
      <style>
        {`
          /* ===== Mobile Sidebar Styles ===== */
          .mobile-menu-backdrop { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 21, 41, 0.6); z-index: 1150; opacity: 0; transition: opacity 0.4s ease; pointer-events: none; }
          .mobile-menu-backdrop.open { opacity: 1; pointer-events: auto; }
          .mobile-sidebar { position: fixed; top: 0; left: 0; height: 100%; width: 300px; background: #fff; z-index: 1200; transform: translateX(-100%); transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1); display: flex; flex-direction: column; }
          .mobile-sidebar.open { transform: translateX(0); }
          .mobile-sidebar-header { padding: 1rem 1.5rem; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #e5e7eb; }
          .mobile-sidebar-header .close-btn { background: none; border: none; font-size: 1.5rem; color: #334155; cursor: pointer; }
          .mobile-nav-list { padding: 1rem; list-style: none; margin: 0; overflow-y: auto; flex-grow: 1; }
          .mobile-nav-link, .mobile-submenu-toggle { display: flex; justify-content: space-between; align-items: center; padding: 0.75rem 1rem; text-decoration: none; color: #002956; font-weight: 500; border-radius: 6px; transition: background-color 0.2s ease, color 0.2s ease; cursor: pointer; }
          .mobile-nav-link:hover, .mobile-submenu-toggle:hover { background-color: #f1f5f9; }
          .mobile-nav-link.active { color: #00a0e9; background-color: #e0f2fe; }
          .mobile-submenu { list-style: none; padding-left: 1rem; overflow: hidden; max-height: 0; transition: max-height 0.4s ease-in-out; }
          .mobile-submenu.open { max-height: 500px; }
          .mobile-submenu-link { display: block; padding: 0.6rem 1rem; text-decoration: none; color: #334155; border-radius: 6px; }
          .mobile-submenu-link:hover { background-color: #f1f5f9; }
          .mobile-submenu-toggle .fa-chevron-down { transition: transform 0.3s ease; }
          .mobile-submenu-toggle.open .fa-chevron-down { transform: rotate(180deg); }
          .mobile-sub-dropdown { list-style: none; padding-left: 1rem; overflow: hidden; max-height: 0; transition: max-height 0.4s ease-in-out; }
          .mobile-sub-dropdown.open { max-height: 300px; }
          .mobile-sub-dropdown-toggle { display: flex; justify-content: space-between; align-items: center; padding: 0.6rem 1rem; text-decoration: none; color: #334155; border-radius: 6px; transition: background-color 0.2s ease, color 0.2s ease; cursor: pointer; margin-left: 0.5rem; }
          .mobile-sub-dropdown-toggle:hover { background-color: #f1f5f9; }
          .mobile-sub-dropdown-toggle .fa-chevron-down { transition: transform 0.3s ease; }
          .mobile-sub-dropdown-toggle.open .fa-chevron-down { transform: rotate(180deg); }
          .mobile-sub-dropdown-link { display: block; padding: 0.5rem 1rem; text-decoration: none; color: #6b7280; border-radius: 6px; margin-left: 1rem; }
          .mobile-sub-dropdown-link:hover { background-color: #f1f5f9; }

          /* ===== Desktop Styles ===== */
          .desktop-nav-link { color: #002956; font-weight: 600; font-size: 15px; letter-spacing: 0.5px; padding: 12px 16px; position: relative; text-decoration: none; display: block; white-space: nowrap; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); border-radius: 8px; }
          .desktop-nav-link.active { color: #00a0e9; }
          .header-search-container {
              position: relative;
          }
          .header-search-popup {
              position: absolute;
              top: 100%;
              right: 0;
              margin-top: 10px;
              width: 300px;
              background: #fff;
              border-radius: 8px;
              box-shadow: 0 4px 12px rgba(0, 41, 86, 0.15);
              padding: 1rem;
              z-index: 1100;
          }
          .header-search-input {
              width: 100%;
              padding: 8px 15px;
              padding-right: 40px;
              color:rgb(9, 30, 70);
              border-radius: 20px;
              border: 1px solid #e5e7eb;
              font-size: 14px;
              background-color: #f0f9ff;
          }
              .header-search-input ::placeholder{
              color: #00a0e9;
              }
          .header-search-input:focus {
              outline: none;
              border-color: #00a0e9;
              box-shadow: 0 0 0 3px rgba(0, 160, 233, 0.2);
          }
          .header-search-button {
              background: none;
              border: none;
              color: #00a0e9;
              cursor: pointer;
              font-size: 1.2rem;
          }
          .header-search-results {
              position: absolute;
              top: 100%;
              left: 0;
              right: 0;
              background: white;
              border-radius: 8px;
              box-shadow: 0 4px 12px rgba(0, 41, 86, 0.15);
              border: 1px solid #e0e7ff;
              margin-top: 8px;
              max-height: 300px;
              overflow-y: auto;
              z-index: 1000;
          }
          .main-menu-panel{flex:0 0 45%;border-right:1px solid rgba(0,41,86,.1);padding-right:12px}.sub-menu-panel{flex:1 1 auto;padding-left:20px}.sub-menu-panel-content{animation:fadeIn .4s ease}@keyframes fadeIn{0%{opacity:0;transform:translateX(10px)}to{opacity:1;transform:translateX(0)}}.main-menu-item{display:flex;justify-content:space-between;align-items:center;padding:12px 16px;border-radius:8px;text-decoration:none;font-weight:500;color:#002956;transition:all .2s ease-in-out;cursor:pointer}.main-menu-item.active,.main-menu-item:hover{background-color:rgba(0,160,233,.1);color:#00a0e9;transform:translateX(4px)}.sub-menu-item{display:block;padding:10px 16px;border-radius:6px;text-decoration:none;font-weight:400;color:#334155;transition:all .2s ease-in-out}.sub-menu-item:hover{background-color:rgba(0,41,86,.05);color:#002956}.sub-menu-panel h3{font-size:1rem;font-weight:600;color:#002956;margin-bottom:1rem;padding-bottom:.5rem;border-bottom:1px solid rgba(0,41,86,.1)}

          /* Hamburger Icon Style */
            .hamburger-icon {
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                width: 24px;
                height: 24px;
                background: white;
                border: none;
                padding: 0;
                box-sizing: border-box;
                cursor: pointer;
            }

            .hamburger-icon span {
                width: 100%;
                height: 2px;
                background-color: #666;
                display: block;
                transition: all 0.3s ease-in-out;
            }


          @media (min-width: 990px) and (max-width: 1200px) {
            header.sticky-top .container-fluid {
              padding-left: 16px;
              padding-right: 16px;
              flex-wrap: wrap;
            }
            .navbar-brand img {
              height: 42px;
              max-width: 140px;
            }
            nav.d-lg-flex {
              flex-grow: 1;
              justify-content: center;
              margin-left: 20px;
            }
            .navbar-nav.flex-row {
              flex-wrap: wrap;
              gap: 12px;
            }
            .desktop-nav-link {
              font-size: 14px;
              padding: 6px 10px;
              white-space: nowrap;
            }
            .desktop-search-item {
              display: none !important; /* hide search on tab view to prevent overflow */
            }
            .header-search-container {
              width: 100%;
            }
            .mega-menu {
              left: 50% !important;
              transform: translateX(-50%) !important;
              width: 90vw !important;
            }
          }
        `}
      </style>

      <header className="sticky-top" style={headerStyle} ref={headerRef}>
        <div className="container-fluid px-4 lg:px-8 xl:px-12 d-flex justify-content-between align-items-center">
          <Link to="/" className="navbar-brand">
            <img
              src={MakonisLogo}
              alt="Makonis Software"
              className={`${transitionAllDuration500EaseOut} h-12 w-auto`}
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="d-none d-lg-flex align-items-center">
            <ul className="navbar-nav flex-row align-items-center">
              {navItems.map((item) => (
                <li
                  key={item.name}
                  className="nav-item dropdown"
                  onMouseLeave={item.isMegaMenu ? handleMenuClose : undefined}
                >
                  {item.isMegaMenu ? (
                    <>
                      <Link
                        to="#"
                        className={`desktop-nav-link ${
                          location.pathname.startsWith(item.path)
                            ? "active"
                            : ""
                        }`}
                        onMouseEnter={(e) => {
                          e.preventDefault();
                          handleMenuEnter();
                          setOpenDropdown(item.name);
                          // START: FIX - Conditionally set active sub-menu
                          if (item.name === "Business") {
                            setActiveSubMenu("IT Services"); // Default for Business
                          } else {
                            setActiveSubMenu(""); // Clear for "Who we are"
                          }
                          // END: FIX
                        }}
                        onClick={(e) => e.preventDefault()}
                        style={{ display: "flex", alignItems: "center" }}
                      >
                        {item.name}
                        <IoIosArrowDown
                          style={{
                            width: "16px",
                            height: "16px",
                            marginLeft: "9px",
                            fontWeight: "800",
                          }}
                        />
                      </Link>

                      <div
                        className={`mega-menu ${
                          openDropdown === item.name ? "show" : ""
                        }`}
                        style={{
                          position: "absolute",
                          top: "100%",
                          left: "100%",
                          transform: "translateX(-25%)",
                          background: "transparent",
                          backdropFilter: "blur(20px)",
                          borderRadius: "16px",
                          width: "600px",
                          display: "flex",
                          marginTop: "16px",
                          zIndex: 1000,
                          visibility:
                            openDropdown === item.name ? "visible" : "hidden",
                          opacity: openDropdown === item.name ? 1 : 0,
                          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                        }}
                        onMouseEnter={handleMenuEnter}
                        onMouseLeave={handleMenuClose}
                      >
                        {/* START: FIX - Conditional rendering for mega menus */}
                        {item.name === "Who we are" ? (
                          <div style={{ background: "#fff", borderRadius: "10px", padding: "10px", width: "250px" }}>
                            {item.megaMenuItems.map((subItem) => (
                              <Link
                                key={subItem.name}
                                to={subItem.path}
                                className="sub-menu-item"
                                onClick={() => setOpenDropdown("")}
                              >
                                {subItem.name}
                              </Link>
                            ))}
                          </div>
                        ) : (
                          <>
                            <div
                              className="main-menu-panel"
                              style={{
                                flex: "0 0 45%",
                                background: "#fff",
                                padding: "10px",
                                borderRadius: "10px",
                                paddingRight: "12px",
                                height: "180px",
                              }}
                            >
                              {item.megaMenuItems.map((mainItem) => (
                                <div
                                  key={mainItem.name}
                                  className={`main-menu-item ${
                                    activeSubMenu === mainItem.name ? "active" : ""
                                  }`}
                                  style={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    padding: "12px 16px",
                                    borderRadius: "8px",
                                    textDecoration: "none",
                                    fontWeight: "500",
                                    color:
                                      activeSubMenu === mainItem.name
                                        ? "#00a0e9"
                                        : "#002956",
                                    backgroundColor:
                                      activeSubMenu === mainItem.name
                                        ? "rgba(0, 160, 233, 0.1)"
                                        : "transparent",
                                    transition: "all 0.2s ease-in-out",
                                    cursor: "pointer",
                                  }}
                                  onMouseEnter={() =>
                                    setActiveSubMenu(mainItem.name)
                                  }
                                >
                                  <span>{mainItem.name}</span>
                                  {item.name !== "Who we are" && (
                                    <lord-icon
                                      src="https://cdn.lordicon.com/zmkotitn.json"
                                      trigger="hover"
                                      style={{ width: "14px", height: "14px" }}
                                    ></lord-icon>
                                  )}
                                </div>
                              ))}
                            </div>
                            {item.name === "Business" &&
                              subMenuData?.subMenuItems && (
                                <div
                                  className="sub-menu-panel"
                                  style={{
                                    flex: "1 1 auto",
                                    paddingLeft: "20px",
                                    marginLeft: "10px",
                                    padding: "12px",
                                    background: "#fff",
                                    borderRadius: "10px",
                                  }}
                                >
                                  <div className="sub-menu-panel-content">
                                    <h3
                                      style={{
                                        fontSize: "1rem",
                                        fontWeight: "600",
                                        color: "#002956",
                                        marginBottom: "1rem",
                                        paddingBottom: "0.5rem",
                                        borderBottom:
                                          "1px solid rgba(0, 41, 86, 0.1)",
                                      }}
                                    >
                                      {subMenuData.name}
                                    </h3>
                                    {subMenuData.subMenuItems.map((subItem) => (
                                      <Link
                                        to={subItem.path}
                                        key={subItem.name}
                                        className="sub-menu-item"
                                        style={{
                                          display: "block",
                                          padding: "10px 16px",
                                          borderRadius: "6px",
                                          textDecoration: "none",
                                          fontWeight: "400",
                                          color: "#334155",
                                          transition: "all 0.2s ease-in-out",
                                        }}
                                        onClick={() => setOpenDropdown("")}
                                      >
                                        {subItem.name}
                                      </Link>
                                    ))}
                                  </div>
                                </div>
                              )}
                          </>
                        )}
                          {/* END: FIX */}
                      </div>
                    </>
                  ) : (
                    <Link
                      to={item.path}
                      className={`desktop-nav-link ${
                        location.pathname === item.path ? "active" : ""
                      }`}
                    >
                      {item.name}
                    </Link>
                  )}
                </li>
              ))}
              <li
                className="nav-item ms-2"
                ref={desktopSearchRef}
              >
                <div className="header-search-container">
                    <button
                        className="header-search-button"
                        onClick={() => setIsDesktopSearchOpen(!isDesktopSearchOpen)}
                    >
                        <i className="fas fa-search"></i>
                    </button>
                    {isDesktopSearchOpen && (
                        <div className="header-search-popup">
                        <form
                            onSubmit={handleSearchSubmit}
                        >
                            <input
                            type="text"
                            className="header-search-input"
                            placeholder="Search..."
                            value={searchQuery}
                            onChange={(e) => handleDesktopSearch(e.target.value)}
                            onKeyDown={(e) => {
                                if (e.key === "Escape") {
                                setIsDesktopSearchOpen(false);
                                setSearchQuery("");
                                setSearchResults([]);
                                }
                            }}
                            />
                            {/* Search Results Dropdown */}
                            {searchQuery.length >= 2 && (
                            <div className="header-search-results">
                                {searchResults.length > 0 ? (
                                <>
                                    <div
                                    className="px-3 py-2 border-bottom"
                                    style={{
                                        fontSize: "12px",
                                        color: "#6b7280",
                                        fontWeight: "600",
                                    }}
                                    >
                                    Search Results
                                    </div>
                                    {searchResults.map((result, index) => (
                                    <div
                                        key={result.id}
                                        className="search-result-item"
                                        style={{
                                        padding: "12px 16px",
                                        cursor: "pointer",
                                        borderBottom:
                                            index < searchResults.length - 1
                                            ? "1px solid #f1f5f9"
                                            : "none",
                                        transition: "background-color 0.2s ease",
                                        }}
                                        onClick={() => handleSearchResultClick(result)}
                                        onMouseEnter={(e) =>
                                        (e.target.style.backgroundColor = "#f8fafc")
                                        }
                                        onMouseLeave={(e) =>
                                        (e.target.style.backgroundColor = "transparent")
                                        }
                                    >
                                        <div className="d-flex align-items-center">
                                        <lord-icon
                                            src="https://cdn.lordicon.com/kiynvdns.json"
                                            trigger="hover"
                                            colors="primary:#0d6efd"
                                            style={{
                                            width: "16px",
                                            height: "16px",
                                            marginRight: "12px",
                                            }}
                                        ></lord-icon>
                                        <div className="flex-grow-1">
                                            <div
                                            style={{
                                                fontWeight: "500",
                                                color: "#002956",
                                                fontSize: "14px",
                                            }}
                                            >
                                            {result.title}
                                            </div>
                                            <div
                                            style={{
                                                fontSize: "12px",
                                                color: "#6b7280",
                                                textTransform: "capitalize",
                                            }}
                                            >
                                            {result.type}
                                            </div>
                                        </div>
                                        <lord-icon
                                            src="https://cdn.lordicon.com/zmkotitn.json"
                                            trigger="hover"
                                            colors="primary:#9ca3af"
                                            style={{ width: "14px", height: "14px" }}
                                        ></lord-icon>
                                        </div>
                                    </div>
                                    ))}
                                </>
                                ) : searchQuery.length >= 2 && !isSearching ? (
                                <div
                                    className="text-center py-4"
                                    style={{ color: "#6b7280" }}
                                >
                                    <lord-icon
                                    src="https://cdn.lordicon.com/xfftupfv.json"
                                    trigger="hover"
                                    style={{
                                        width: "32px",
                                        height: "32px",
                                        display: "block",
                                        marginBottom: "8px",
                                    }}
                                    ></lord-icon>
                                    <div>No results found</div>
                                    <small>Try a different search term</small>
                                </div>
                                ) : null}
                            </div>
                            )}
                        </form>
                        </div>
                    )}
                    </div>
                </li>
            </ul>
          </nav>

          {/* Mobile Search and Hamburger Icons */}
          <div className="d-lg-none d-flex align-items-center">
            <button
              className="btn me-2"
              type="button"
              onClick={() => setIsMobileSearchOpen(true)}
              style={{
                background: "transparent",
                border: "none",
                color: "#00a0e9",
                fontSize: "1.2rem",
              }}
            >
              <i className="fas fa-search"></i>
            </button>
            <button className="btn" type="button" onClick={toggleMobileMenu}>
                <div className="hamburger-icon">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </button>
          </div>
        </div>
      </header>

      {/* Mobile Sidebar Navigation */}
      <div
        className={`mobile-menu-backdrop d-lg-none ${
          mobileMenuOpen ? "open" : ""
        }`}
        onClick={toggleMobileMenu}
      ></div>
      <div
        className={`mobile-sidebar d-lg-none ${mobileMenuOpen ? "open" : ""}`}
      >
        <div className="mobile-sidebar-header">
          <span className="fw-bold fs-5" style={{ color: "#002956" }}>
            Makonis
          </span>
          <button onClick={toggleMobileMenu} className="close-btn">
            &times;
          </button>
        </div>
        <ul className="mobile-nav-list">
          {mobileNavItems.map((item) => (
            <li key={item.name} className="mb-2">
              {item.isDropdown ? (
                <>
                  <div
                    className={`mobile-submenu-toggle ${
                      openMobileSubMenu === item.name ? "open" : ""
                    }`}
                    onClick={() => toggleMobileSubMenu(item.name)}
                  >
                    <span>{item.name}</span>
                    <lord-icon
                      src="https://cdn.lordicon.com/xcrjfuzb.json"
                      trigger="hover"
                      style={{ width: "16px", height: "16px" }}
                    ></lord-icon>
                  </div>
                  <ul
                    className={`mobile-submenu ${
                      openMobileSubMenu === item.name ? "open" : ""
                    }`}
                  >
                    {item.dropdownItems.map((subItem) => (
                      <li key={subItem.name}>
                        {subItem.isSubDropdown ? (
                          <>
                            <div
                              className={`mobile-sub-dropdown-toggle ${
                                openMobileSubDropdown === subItem.name
                                  ? "open"
                                  : ""
                              }`}
                              onClick={() =>
                                toggleMobileSubDropdown(subItem.name)
                              }
                            >
                              <span>{subItem.name}</span>
                              <lord-icon
                                src="https://cdn.lordicon.com/xcrjfuzb.json"
                                trigger="hover"
                                style={{ width: "14px", height: "14px" }}
                              ></lord-icon>
                            </div>
                            <ul
                              className={`mobile-sub-dropdown ${
                                openMobileSubDropdown === subItem.name
                                  ? "open"
                                  : ""
                              }`}
                            >
                              {subItem.subDropdownItems.map((subSubItem) => (
                                <li key={subSubItem.name}>
                                  <Link
                                    to={subSubItem.path}
                                    className="mobile-sub-dropdown-link"
                                    onClick={toggleMobileMenu}
                                  >
                                    {subSubItem.name}
                                  </Link>
                                </li>
                              ))}
                            </ul>
                          </>
                        ) : (
                          <Link
                            to={subItem.path}
                            className="mobile-submenu-link"
                            onClick={toggleMobileMenu}
                          >
                            {subItem.name}
                          </Link>
                        )}
                      </li>
                    ))}
                  </ul>
                </>
              ) : (
                <Link
                  to={item.path}
                  className={`mobile-nav-link ${
                    location.pathname === item.path ? "active" : ""
                  }`}
                  onClick={toggleMobileMenu}
                >
                  {item.name}
                </Link>
              )}
            </li>
          ))}
        </ul>
      </div>

      {/* Mobile Search Overlay */}
      <SearchOverlay
        isOpen={isMobileSearchOpen}
        onClose={() => setIsMobileSearchOpen(false)}
      />
    </>
  );
};

export default Header;